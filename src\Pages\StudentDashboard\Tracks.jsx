import React from 'react';
import { motion } from 'framer-motion';
import { FiFileText } from 'react-icons/fi';

const Tracks = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="student-card rounded-2xl p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="student-icon-wrapper">
            <FiFileText className="text-2xl text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold student-gradient-text">Tracks</h1>
            <p className="text-gray-600">View your academic tracks and progress</p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Under Construction</h2>
          <p className="text-gray-600">This page is currently being developed. Please check back later for more content.</p>
        </div>
      </div>
    </motion.div>
  );
};

export default Tracks;
