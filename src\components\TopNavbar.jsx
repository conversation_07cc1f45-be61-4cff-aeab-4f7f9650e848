import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiMenu } from "react-icons/fi";
import { useAuth } from "../contexts/AuthContext";

const TopNavbar = ({ onToggleSidebar, sidebarCollapsed }) => {
  const { user } = useAuth();

  const getRoleColor = () => {
    switch (user?.role) {
      case "student":
        return "from-blue-600 to-blue-700";
      case "doctor":
        return "from-green-600 to-green-700";
      case "affairs":
        return "from-purple-600 to-purple-700";
      case "admin":
        return "from-red-600 to-red-700";
      default:
        return "from-gray-600 to-gray-700";
    }
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
      className={`bg-gradient-to-r ${getRoleColor()} shadow-lg fixed top-0 right-0 z-20 transition-all duration-300`}
      style={{
        left: sidebarCollapsed ? "80px" : "280px",
        width: sidebarCollapsed ? "calc(100% - 80px)" : "calc(100% - 280px)",
      }}
    >
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left Section */}
          <div className="flex items-center space-x-4">
            <motion.button
              onClick={onToggleSidebar}
              className="lg:hidden p-2 rounded-lg hover:bg-white/20 transition-colors text-white"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FiMenu className="text-xl" />
            </motion.button>

            <div className="text-white">
              <h1 className="text-xl font-bold">
                {user?.role === "student" && "Student Dashboard"}
                {user?.role === "doctor" && "Doctor Dashboard"}
                {user?.role === "affairs" && "Affairs Dashboard"}
                {user?.role === "admin" && "Admin Dashboard"}
              </h1>
              <p className="text-sm opacity-90">
                Welcome back, {user?.email || user?.collegeId || "User"}!
              </p>
            </div>
          </div>

          {/* Right Section */}
          <div className="flex items-center space-x-4">
            {/* Search Bar */}
            <div className="hidden md:flex items-center bg-white/20 rounded-lg px-4 py-2">
              <FiSearch className="text-white/70 mr-2" />
              <input
                type="text"
                placeholder="Search..."
                className="bg-transparent text-white placeholder-white/70 outline-none w-64"
              />
            </div>

            {/* Notifications */}
            <motion.button
              className="relative p-2 rounded-lg hover:bg-white/20 transition-colors text-white"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FiBell className="text-xl" />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                3
              </span>
            </motion.button>

            {/* User Avatar */}
            <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white font-semibold">
              {(user?.email || user?.collegeId || "U").charAt(0).toUpperCase()}
            </div>
          </div>
        </div>
      </div>
    </motion.nav>
  );
};

export default TopNavbar;
