import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  FiHome,
  FiBook,
  FiMap,
  FiUsers,
  FiMessageCircle,
  FiFileText,
  FiUser,
  FiSettings,
  FiLogOut,
  FiChevronLeft,
  FiChevronRight,
  FiCalendar,
  FiClock,
  FiUserCheck,
  FiInbox,
  FiTarget,
} from "react-icons/fi";
import {
  FaUserGraduate,
  FaUserMd,
  FaUserTie,
  FaChalkboardTeacher,
} from "react-icons/fa";
import Swal from "sweetalert2";
import { useAuth } from "../contexts/AuthContext";

const Sidebar = ({ isCollapsed, onToggle }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const getMenuItems = () => {
    if (!user) return [];

    switch (user.role) {
      case "student":
        return [
          { path: "/student", icon: FiHome, label: "Dashboard" },
          {
            path: "/student/college-guide",
            icon: FiBook,
            label: "College Guide",
          },
          {
            path: "/student/gpa-calculator",
            icon: FiTarget,
            label: "GPA Calculator",
          },
          {
            path: "/student/learning-resources",
            icon: FiBook,
            label: "Learning Resources",
          },
          { path: "/student/tracks", icon: FiFileText, label: "Tracks" },
          { path: "/student/maps", icon: FiMap, label: "Maps" },
          {
            path: "/student/ta-evaluation",
            icon: FiUserCheck,
            label: "TA & Grader Evaluation",
          },
          {
            path: "/student/ta-attendance",
            icon: FiClock,
            label: "TA Attendance Board",
          },
          {
            path: "/student/chat-bot",
            icon: FiMessageCircle,
            label: "Chat Bot",
          },
          {
            path: "/student/complaints",
            icon: FiInbox,
            label: "Complaints & Inquiries",
          },
          {
            path: "/student/student-page",
            icon: FaUserGraduate,
            label: "Student Page",
          },
        ];
      case "doctor":
        return [
          { path: "/doctor", icon: FiHome, label: "Dashboard" },
          {
            path: "/doctor/teacher-page",
            icon: FaChalkboardTeacher,
            label: "Teacher Page",
          },
          {
            path: "/doctor/learning-resources",
            icon: FiBook,
            label: "Learning Resources",
          },
          { path: "/doctor/tracks", icon: FiFileText, label: "Tracks" },
          {
            path: "/doctor/ta-attendance",
            icon: FiClock,
            label: "TA Attendance Board",
          },
          {
            path: "/doctor/absence-system",
            icon: FiUsers,
            label: "Absence System",
          },
        ];
      case "affairs":
        return [
          { path: "/affairs", icon: FiHome, label: "Dashboard" },
          {
            path: "/affairs/respond-inquiries",
            icon: FiInbox,
            label: "Respond to Inquiries",
          },
          {
            path: "/affairs/schedule-appointment",
            icon: FiCalendar,
            label: "Schedule Appointment",
          },
          { path: "/affairs/timetables", icon: FiClock, label: "Timetables" },
        ];
      case "admin":
        return [
          { path: "/admin", icon: FiHome, label: "Dashboard" },
          { path: "/admin/system", icon: FiSettings, label: "System" },
          { path: "/admin/requests", icon: FiInbox, label: "Requests" },
          { path: "/admin/manage-users", icon: FiUsers, label: "Manage Users" },
        ];
      default:
        return [];
    }
  };

  const getRoleIcon = () => {
    switch (user?.role) {
      case "student":
        return FaUserGraduate;
      case "doctor":
        return FaUserMd;
      case "affairs":
        return FaUserTie;
      case "admin":
        return FiSettings;
      default:
        return FiUser;
    }
  };

  const getRoleColor = () => {
    switch (user?.role) {
      case "student":
        return "from-blue-600 to-blue-700";
      case "doctor":
        return "from-green-600 to-green-700";
      case "affairs":
        return "from-purple-600 to-purple-700";
      case "admin":
        return "from-red-600 to-red-700";
      default:
        return "from-gray-600 to-gray-700";
    }
  };

  const handleLogout = () => {
    Swal.fire({
      title: "Are you sure?",
      text: "You will be logged out of your account.",
      icon: "question",
      showCancelButton: true,
      confirmButtonColor: "#EF4444",
      cancelButtonColor: "#6B7280",
      confirmButtonText: "Yes, logout",
      cancelButtonText: "Cancel",
    }).then((result) => {
      if (result.isConfirmed) {
        logout();
        navigate("/");
        Swal.fire({
          icon: "success",
          title: "Logged Out",
          text: "You have been successfully logged out.",
          timer: 2000,
          showConfirmButton: false,
        });
      }
    });
  };

  const menuItems = getMenuItems();
  const RoleIcon = getRoleIcon();

  return (
    <motion.div
      initial={{ width: isCollapsed ? 80 : 280 }}
      animate={{ width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="bg-white shadow-xl border-r border-gray-200 h-screen flex flex-col relative z-10"
    >
      {/* Header */}
      <div
        className={`bg-gradient-to-r ${getRoleColor()} p-4 text-white`}
      >
        <div className="flex items-center justify-between">
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="flex items-center space-x-3"
              >
                <RoleIcon className="text-2xl" />
                <div>
                  <h2 className="font-bold text-lg">SmartZone</h2>
                  <p className="text-sm opacity-90 capitalize">
                    {user?.role} Portal
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <motion.button
            onClick={onToggle}
            className="p-2 rounded-lg hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {isCollapsed ? <FiChevronRight /> : <FiChevronLeft />}
          </motion.button>
        </div>
      </div>

      {/* User Info */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div
            className={`w-10 h-10 rounded-full bg-gradient-to-r ${getRoleColor()} flex items-center justify-center text-white`}
          >
            <RoleIcon className="text-lg" />
          </div>
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <p className="font-semibold text-gray-800">
                  {user?.email || user?.collegeId || "User"}
                </p>
                <p className="text-sm text-gray-600 capitalize">{user?.role}</p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1 px-3">
          {menuItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 group ${
                    isActive
                      ? `bg-gradient-to-r ${getRoleColor()} text-white shadow-md`
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <item.icon
                    className={`text-xl ${
                      isActive
                        ? "text-white"
                        : "text-gray-500 group-hover:text-gray-700"
                    }`}
                  />
                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="font-medium"
                      >
                        {item.label}
                      </motion.span>
                    )}
                  </AnimatePresence>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Logout Button */}
      <div className="p-4 border-t border-gray-200">
        <motion.button
          onClick={handleLogout}
          className="w-full flex items-center space-x-3 px-3 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 group"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <FiLogOut className="text-xl" />
          <AnimatePresence>
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="font-medium"
              >
                Logout
              </motion.span>
            )}
          </AnimatePresence>
        </motion.button>
      </div>
    </motion.div>
  );
};

export default Sidebar;
