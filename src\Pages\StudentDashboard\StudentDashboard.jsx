import React from "react";
import { Routes, Route } from "react-router-dom";

// Import page components
import CollegeGuide from "./CollegeGuide";
import GPACalculator from "./GPACalculator";
import LearningResources from "./LearningResources";
import Tracks from "./Tracks";
import Maps from "./Maps";
import TAEvaluation from "./TAEvaluation";
import TAAttendance from "./TAAttendance";
import ChatBot from "./ChatBot";
import Complaints from "./Complaints";
import StudentPage from "./StudentPage";

const StudentDashboard = () => {
  return (
    <div className="student-dashboard">
      <Routes>
        <Route path="/" element={<DashboardHome />} />
        <Route path="/college-guide" element={<CollegeGuide />} />
        <Route path="/gpa-calculator" element={<GPACalculator />} />
        <Route path="/learning-resources" element={<LearningResources />} />
        <Route path="/tracks" element={<Tracks />} />
        <Route path="/maps" element={<Maps />} />
        <Route path="/ta-evaluation" element={<TAEvaluation />} />
        <Route path="/ta-attendance" element={<TAAttendance />} />
        <Route path="/chat-bot" element={<ChatBot />} />
        <Route path="/complaints" element={<Complaints />} />
        <Route path="/student-page" element={<StudentPage />} />
      </Routes>
    </div>
  );

  function DashboardHome() {
    return (
      <>
        <h1>Student Dashboard</h1>
      </>
    );
  }
};

export default StudentDashboard;
