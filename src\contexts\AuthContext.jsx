import { createContext, useContext, useState, useEffect } from "react";

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [pendingRequests, setPendingRequests] = useState([]);

  // Load user from localStorage on app start
  useEffect(() => {
    const savedUser = localStorage.getItem("smartzone_user");
    const savedRequests = localStorage.getItem("smartzone_pending_requests");

    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }

    if (savedRequests) {
      setPendingRequests(JSON.parse(savedRequests));
    }

    setIsLoading(false);
  }, []);

  // Save user to localStorage whenever user changes
  useEffect(() => {
    if (user) {
      localStorage.setItem("smartzone_user", JSON.stringify(user));
    } else {
      localStorage.removeItem("smartzone_user");
    }
  }, [user]);

  // Save pending requests to localStorage
  useEffect(() => {
    localStorage.setItem(
      "smartzone_pending_requests",
      JSON.stringify(pendingRequests)
    );
  }, [pendingRequests]);

  const login = (userData) => {
    setUser(userData);
  };

  const register = (registrationData) => {
    // Add to pending requests for admin approval
    const newRequest = {
      id: Date.now().toString(),
      ...registrationData,
      status: "pending",
      submittedAt: new Date().toISOString(),
    };

    setPendingRequests((prev) => [...prev, newRequest]);
    return newRequest;
  };

  const approveRequest = (requestId) => {
    const request = pendingRequests.find((req) => req.id === requestId);
    if (request) {
      // Remove from pending requests
      setPendingRequests((prev) => prev.filter((req) => req.id !== requestId));

      // Create user account
      const newUser = {
        id: requestId,
        collegeId: request.collegeId,
        firstName: request.firstName,
        lastName: request.lastName,
        role: request.role,
        department: request.department,
        status: "approved",
      };

      return newUser;
    }
    return null;
  };

  const rejectRequest = (requestId) => {
    setPendingRequests((prev) => prev.filter((req) => req.id !== requestId));
  };

  const logout = () => {
    setUser(null);
  };

  const value = {
    user,
    isLoading,
    pendingRequests,
    login,
    register,
    logout,
    approveRequest,
    rejectRequest,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
