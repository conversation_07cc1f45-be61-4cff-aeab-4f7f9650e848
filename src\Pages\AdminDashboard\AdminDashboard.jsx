import React from "react";
import { Routes, Route } from "react-router-dom";

// Import page components
import System from "./System";
import Requests from "./Requests";
import ManageUsers from "./ManageUsers";

const AdminDashboard = () => {
  return (
    <div className="admin-dashboard">
      <Routes>
        <Route path="/" element={<DashboardHome />} />
        <Route path="/system" element={<System />} />
        <Route path="/requests" element={<Requests />} />
        <Route path="/manage-users" element={<ManageUsers />} />
      </Routes>
    </div>
  );

  function DashboardHome() {
    return (
      <>
        <h1>Admin Dashboard</h1>
      </>
    );
  }
};

export default AdminDashboard;
