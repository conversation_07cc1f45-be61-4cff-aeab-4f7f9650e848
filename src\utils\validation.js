// Validation utilities for the SmartZone application

export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password) => {
  const errors = [];

  if (password.length < 8) {
    errors.push("Min 8 chars");
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Need uppercase");
  }

  if (!/[a-z]/.test(password)) {
    errors.push("Need lowercase");
  }

  if (!/\d/.test(password)) {
    errors.push("Need number");
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push("Need special char");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateCollegeId = (collegeId) => {
  const trimmedId = collegeId.toString().trim();

  // Check if empty
  if (trimmedId.length === 0) {
    return {
      isValid: false,
      error: "ID required",
    };
  }

  // Check if numeric only
  const numericRegex = /^\d+$/;
  if (!numericRegex.test(trimmedId)) {
    return {
      isValid: false,
      error: "Numbers only",
    };
  }

  // Check length (typically 6 digits)
  if (trimmedId.length < 4) {
    return {
      isValid: false,
      error: "Min 4 digits",
    };
  }

  if (trimmedId.length > 10) {
    return {
      isValid: false,
      error: "Max 10 digits",
    };
  }

  return {
    isValid: true,
    error: "",
  };
};

export const validateName = (name) => {
  const trimmedName = name.trim();

  // Check if empty
  if (trimmedName.length === 0) {
    return {
      isValid: false,
      error: "Name required",
    };
  }

  // Check minimum length
  if (trimmedName.length < 2) {
    return {
      isValid: false,
      error: "Min 2 chars",
    };
  }

  // Check maximum length
  if (trimmedName.length > 50) {
    return {
      isValid: false,
      error: "Max 50 chars",
    };
  }

  // Check for English letters only (and spaces)
  const englishOnlyRegex = /^[A-Za-z\s]+$/;
  if (!englishOnlyRegex.test(trimmedName)) {
    return {
      isValid: false,
      error: "English letters only",
    };
  }

  // Check for consecutive spaces
  if (/\s{2,}/.test(trimmedName)) {
    return {
      isValid: false,
      error: "No double spaces",
    };
  }

  // Check if starts or ends with space
  if (trimmedName !== name.trim()) {
    return {
      isValid: false,
      error: "No leading/trailing spaces",
    };
  }

  return {
    isValid: true,
    error: "",
  };
};

export const validateDepartment = (department) => {
  const trimmedDept = department.trim();

  // Check if empty
  if (trimmedDept.length === 0) {
    return {
      isValid: false,
      error: "Department required",
    };
  }

  // Check minimum length
  if (trimmedDept.length < 2) {
    return {
      isValid: false,
      error: "Min 2 chars",
    };
  }

  // Check maximum length
  if (trimmedDept.length > 100) {
    return {
      isValid: false,
      error: "Max 100 chars",
    };
  }

  // Check for English letters, numbers, spaces, and common symbols only
  const validDeptRegex = /^[A-Za-z0-9\s&\-().,]+$/;
  if (!validDeptRegex.test(trimmedDept)) {
    return {
      isValid: false,
      error: "English letters & symbols only",
    };
  }

  // Check for consecutive spaces
  if (/\s{2,}/.test(trimmedDept)) {
    return {
      isValid: false,
      error: "No double spaces",
    };
  }

  return {
    isValid: true,
    error: "",
  };
};

export const validateSmartZoneEmail = (email) => {
  const trimmedEmail = email.trim().toLowerCase();

  // Check if empty
  if (trimmedEmail.length === 0) {
    return {
      isValid: false,
      error: "Email required",
    };
  }

  // Check basic email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(trimmedEmail)) {
    return {
      isValid: false,
      error: "Invalid email format",
    };
  }

  // Check if it ends with @smartzone.edu
  if (!trimmedEmail.endsWith("@smartzone.edu")) {
    return {
      isValid: false,
      error: "Must end with @smartzone.edu",
    };
  }

  // Check local part (before @)
  const localPart = trimmedEmail.split("@")[0];
  if (localPart.length < 3) {
    return {
      isValid: false,
      error: "Username min 3 chars",
    };
  }

  // Check for valid characters in local part
  const validLocalRegex = /^[a-z0-9._-]+$/;
  if (!validLocalRegex.test(localPart)) {
    return {
      isValid: false,
      error: "Invalid username chars",
    };
  }

  return {
    isValid: true,
    error: "",
  };
};

export const validateFile = (file, type = "image") => {
  if (!file) {
    return {
      isValid: false,
      error: "File required",
    };
  }

  if (type === "image") {
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png"];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: "JPG/PNG only",
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: "Max 5MB",
      };
    }
  }

  return {
    isValid: true,
    error: "",
  };
};

export const validateForm = (formData, requiredFields) => {
  const errors = {};

  requiredFields.forEach((field) => {
    if (!formData[field] || formData[field].toString().trim() === "") {
      errors[field] = `${
        field.charAt(0).toUpperCase() + field.slice(1)
      } is required`;
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

export const getPasswordStrength = (password) => {
  let score = 0;

  if (password.length >= 8) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[a-z]/.test(password)) score++;
  if (/\d/.test(password)) score++;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;

  // Additional points for longer passwords
  if (password.length >= 12) score++;
  if (password.length >= 16) score++;

  const strength = [
    "Very Weak",
    "Weak",
    "Fair",
    "Good",
    "Strong",
    "Very Strong",
    "Very Strong",
  ];
  const colors = [
    "#ff4444",
    "#ff8800",
    "#ffaa00",
    "#88cc00",
    "#00cc44",
    "#00aa00",
    "#008800",
  ];

  return {
    score,
    strength: strength[score] || "Very Weak",
    color: colors[score] || "#ff4444",
    percentage: Math.min((score / 5) * 100, 100),
  };
};
