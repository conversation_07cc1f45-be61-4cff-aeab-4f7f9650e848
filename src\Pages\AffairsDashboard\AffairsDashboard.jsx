import React from "react";
import { Routes, Route } from "react-router-dom";

// Import page components
import RespondInquiries from "./RespondInquiries";
import ScheduleAppointment from "./ScheduleAppointment";
import Timetables from "./Timetables";

const AffairsDashboard = () => {
  return (
    <div className="affairs-dashboard">
      <Routes>
        <Route path="/" element={<DashboardHome />} />
        <Route path="/respond-inquiries" element={<RespondInquiries />} />
        <Route path="/schedule-appointment" element={<ScheduleAppointment />} />
        <Route path="/timetables" element={<Timetables />} />
      </Routes>
    </div>
  );

  function DashboardHome() {
    return (
      <>
        <h1>Affairs Dashboard</h1>
      </>
    );
  }
};

export default AffairsDashboard;
