import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiUsers, FiSearch, FiTrash2 } from 'react-icons/fi';
import { FaUserGraduate, FaUserMd, FaUserTie } from 'react-icons/fa';
import Swal from 'sweetalert2';

const ManageUsers = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');

  // Mock user data - in a real app, this would come from an API
  const [users, setUsers] = useState([
    { id: 1, name: '<PERSON>', role: 'student', collegeId: '12345', department: 'Computer Science', status: 'active' },
    { id: 2, name: 'Dr. <PERSON>', role: 'doctor', collegeId: '67890', department: 'Computer Science', status: 'active' },
    { id: 3, name: '<PERSON>', role: 'affairs', collegeId: '54321', department: 'Student Services', status: 'active' },
    { id: 4, name: '<PERSON>', role: 'student', collegeId: '98765', department: 'Mathematics', status: 'active' },
    { id: 5, name: 'Dr. <PERSON>', role: 'doctor', collegeId: '11111', department: 'Physics', status: 'active' },
    { id: 6, name: 'Lisa <PERSON>', role: 'affairs', collegeId: '22222', department: 'Academic Affairs', status: 'active' }
  ]);

  const handleDeleteUser = (userId, userName) => {
    Swal.fire({
      title: 'Delete User?',
      text: `Are you sure you want to delete ${userName}? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#EF4444',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'Yes, delete',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        setUsers(users.filter(user => user.id !== userId));
        Swal.fire({
          icon: 'success',
          title: 'User Deleted',
          text: `${userName} has been removed from the system.`,
          confirmButtonColor: '#EF4444'
        });
      }
    });
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'student': return <FaUserGraduate className="text-blue-600" />;
      case 'doctor': return <FaUserMd className="text-green-600" />;
      case 'affairs': return <FaUserTie className="text-purple-600" />;
      default: return <FiUsers className="text-gray-600" />;
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'student': return 'bg-blue-100 text-blue-800';
      case 'doctor': return 'bg-green-100 text-green-800';
      case 'affairs': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.collegeId.includes(searchTerm) ||
                         user.department.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = selectedRole === 'all' || user.role === selectedRole;
    return matchesSearch && matchesRole;
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="admin-card rounded-2xl p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="admin-icon-wrapper">
            <FiUsers className="text-2xl text-red-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold admin-gradient-text">Manage Users</h1>
            <p className="text-gray-600">View and manage all system users</p>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search by name, ID, or department..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
          >
            <option value="all">All Roles</option>
            <option value="student">Students</option>
            <option value="doctor">Doctors</option>
            <option value="affairs">Affairs</option>
          </select>
        </div>

        {/* User Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="admin-system-metric">
            <FiUsers className="text-2xl text-gray-600 mx-auto mb-2" />
            <p className="text-xl font-bold text-gray-800">{users.length}</p>
            <p className="text-sm text-gray-600">Total Users</p>
          </div>
          <div className="admin-system-metric">
            <FaUserGraduate className="text-2xl text-blue-600 mx-auto mb-2" />
            <p className="text-xl font-bold text-gray-800">
              {users.filter(u => u.role === 'student').length}
            </p>
            <p className="text-sm text-gray-600">Students</p>
          </div>
          <div className="admin-system-metric">
            <FaUserMd className="text-2xl text-green-600 mx-auto mb-2" />
            <p className="text-xl font-bold text-gray-800">
              {users.filter(u => u.role === 'doctor').length}
            </p>
            <p className="text-sm text-gray-600">Doctors</p>
          </div>
          <div className="admin-system-metric">
            <FaUserTie className="text-2xl text-purple-600 mx-auto mb-2" />
            <p className="text-xl font-bold text-gray-800">
              {users.filter(u => u.role === 'affairs').length}
            </p>
            <p className="text-sm text-gray-600">Affairs</p>
          </div>
        </div>

        {/* Users List */}
        <div className="space-y-4">
          {filteredUsers.length === 0 ? (
            <div className="text-center py-12">
              <FiUsers className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No Users Found</h3>
              <p className="text-gray-500">Try adjusting your search criteria.</p>
            </div>
          ) : (
            filteredUsers.map((user) => (
              <motion.div
                key={user.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className={`admin-user-card ${user.role}`}
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">
                      {getRoleIcon(user.role)}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">{user.name}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>ID: {user.collegeId}</span>
                        <span>•</span>
                        <span>{user.department}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <span className={`text-xs px-2 py-1 rounded-full ${getRoleColor(user.role)}`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      {user.status}
                    </span>
                    <button
                      onClick={() => handleDeleteUser(user.id, user.name)}
                      className="admin-action-button delete p-2"
                      title="Delete User"
                    >
                      <FiTrash2 className="text-sm" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ManageUsers;
