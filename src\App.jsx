import React, { useState } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { useAuth } from "./contexts/AuthContext";
import StepperForm from "./components/StepperForm";
import Sidebar from "./components/Sidebar";
import TopNavbar from "./components/TopNavbar";

// Dashboard imports will be added as we create them
import StudentDashboard from "./Pages/StudentDashboard/StudentDashboard";
import DoctorDashboard from "./Pages/DoctorDashboard/DoctorDashboard";
import AffairsDashboard from "./Pages/AffairsDashboard/AffairsDashboard";
import AdminDashboard from "./Pages/AdminDashboard/AdminDashboard";

export default function App() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { user, isLoading } = useAuth();

  const toggleSidebar = () => setSidebarCollapsed(!sidebarCollapsed);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return (
      <Router>
        <Routes>
          <Route path="/" element={<StepperForm />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    );
  }

  return (
    <Router>
      <div className="flex h-screen bg-gray-50">
        <Sidebar isCollapsed={sidebarCollapsed} onToggle={toggleSidebar} />

        <div className="flex-1 flex flex-col">
          <TopNavbar
            onToggleSidebar={toggleSidebar}
            sidebarCollapsed={sidebarCollapsed}
          />

          <main className="flex-1 overflow-y-auto pt-20 pb-6 px-6">
            <Routes>
              {/* Student Routes */}
              {user.role === "student" && (
                <>
                  <Route path="/student/*" element={<StudentDashboard />} />
                  <Route
                    path="/"
                    element={<Navigate to="/student" replace />}
                  />
                </>
              )}

              {/* Doctor Routes */}
              {user.role === "doctor" && (
                <>
                  <Route path="/doctor/*" element={<DoctorDashboard />} />
                  <Route path="/" element={<Navigate to="/doctor" replace />} />
                </>
              )}

              {/* Affairs Routes */}
              {user.role === "affairs" && (
                <>
                  <Route path="/affairs/*" element={<AffairsDashboard />} />
                  <Route
                    path="/"
                    element={<Navigate to="/affairs" replace />}
                  />
                </>
              )}

              {/* Admin Routes */}
              {user.role === "admin" && (
                <>
                  <Route path="/admin/*" element={<AdminDashboard />} />
                  <Route path="/" element={<Navigate to="/admin" replace />} />
                </>
              )}

              {/* Fallback */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </main>
        </div>
      </div>
    </Router>
  );
}
