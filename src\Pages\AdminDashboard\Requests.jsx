import React from 'react';
import { motion } from 'framer-motion';
import { FiInbox } from 'react-icons/fi';
import { useAuth } from '../../contexts/AuthContext';
import Swal from 'sweetalert2';

const Requests = () => {
  const { pendingRequests, approveRequest, rejectRequest } = useAuth();

  const handleApprove = (requestId) => {
    Swal.fire({
      title: 'Approve Request?',
      text: 'This will create a new user account.',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10B981',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'Yes, approve',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        const approvedUser = approveRequest(requestId);
        if (approvedUser) {
          Swal.fire({
            icon: 'success',
            title: 'Request Approved!',
            text: 'The user account has been created successfully.',
            confirmButtonColor: '#10B981'
          });
        }
      }
    });
  };

  const handleReject = (requestId) => {
    Swal.fire({
      title: 'Reject Request?',
      text: 'This action cannot be undone.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#EF4444',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'Yes, reject',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        rejectRequest(requestId);
        Swal.fire({
          icon: 'success',
          title: 'Request Rejected',
          text: 'The registration request has been rejected.',
          confirmButtonColor: '#EF4444'
        });
      }
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="admin-card rounded-2xl p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="admin-icon-wrapper">
            <FiInbox className="text-2xl text-red-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold admin-gradient-text">Registration Requests</h1>
            <p className="text-gray-600">Review and manage pending registration requests</p>
          </div>
        </div>

        {pendingRequests.length === 0 ? (
          <div className="text-center py-12">
            <FiInbox className="text-6xl text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No Pending Requests</h3>
            <p className="text-gray-500">All registration requests have been processed.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingRequests.map((request) => (
              <motion.div
                key={request.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="admin-request-card"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <h3 className="text-lg font-semibold text-gray-800">
                        {request.firstName} {request.lastName}
                      </h3>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        request.role === 'doctor' 
                          ? 'bg-green-100 text-green-800'
                          : 'bg-purple-100 text-purple-800'
                      }`}>
                        {request.role.charAt(0).toUpperCase() + request.role.slice(1)}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">College ID:</span> {request.collegeId}
                        </p>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Department:</span> {request.department}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Submitted:</span> {new Date(request.submittedAt).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Status:</span> 
                          <span className="ml-1 text-yellow-600 font-medium">Pending</span>
                        </p>
                      </div>
                    </div>

                    {request.idCardPhoto && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-700 mb-2">ID Card Photo:</p>
                        <div className="w-32 h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                          <span className="text-xs text-gray-500">Photo Uploaded</span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex flex-col space-y-2 ml-4">
                    <button
                      onClick={() => handleApprove(request.id)}
                      className="admin-action-button approve"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => handleReject(request.id)}
                      className="admin-action-button reject"
                    >
                      Reject
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default Requests;
