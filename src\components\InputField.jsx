import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Fi<PERSON>yeOff, FiUpload } from "react-icons/fi";

const InputField = ({
  type = "text",
  label,
  name,
  value,
  onChange,
  placeholder,
  error,
  required = false,
  disabled = false,
  accept,
  className = "",
  showPasswordStrength = false,
  passwordStrength = null,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && onChange) {
      onChange({
        target: {
          name,
          value: file,
        },
      });
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file && onChange) {
      onChange({
        target: {
          name,
          value: file,
        },
      });
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  if (type === "file") {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={`mb-3 ${className}`}
      >
        <label className="block text-sm font-medium text-gray-700 mb-1.5">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <div
          className={`relative border-2 border-dashed rounded-md p-4 text-center transition-colors duration-200 ${
            isDragOver
              ? "border-blue-500 bg-blue-50"
              : error
              ? "border-red-300 bg-red-50"
              : "border-gray-300 hover:border-gray-400"
          } ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <input
            type="file"
            name={name}
            onChange={handleFileChange}
            accept={accept}
            disabled={disabled}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          />
          <FiUpload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-600">
            {value && value.name ? (
              <span className="text-green-600 font-medium">{value.name}</span>
            ) : (
              <>
                Drop your file here or{" "}
                <span className="text-blue-600 font-medium">browse</span>
              </>
            )}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            {accept ? `Accepted formats: ${accept}` : "All file types accepted"}
          </p>
        </div>
        {error && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-1 text-sm text-red-600"
          >
            {error}
          </motion.p>
        )}
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`mb-3 ${className}`}
    >
      <label className="block text-sm font-medium text-gray-700 mb-1.5">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative">
        <input
          type={type === "password" && showPassword ? "text" : type}
          name={name}
          value={value || ""}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          className={`w-full px-3 py-2.5 text-sm border rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-200 ${
            error ? "border-red-300 bg-red-50" : "border-gray-300"
          } ${disabled ? "opacity-50 cursor-not-allowed" : ""} ${
            type === "password" ? "pr-10" : ""
          }`}
        />
        {type === "password" && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            {showPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
          </button>
        )}
      </div>

      {showPasswordStrength && passwordStrength && value && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          className="mt-2"
        >
          <div className="flex items-center justify-between text-xs mb-1">
            <span className="text-gray-600">Password Strength</span>
            <span
              style={{ color: passwordStrength.color }}
              className="font-medium"
            >
              {passwordStrength.strength}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${passwordStrength.percentage}%` }}
              transition={{ duration: 0.3 }}
              className="h-2 rounded-full transition-all duration-300"
              style={{ backgroundColor: passwordStrength.color }}
            />
          </div>
        </motion.div>
      )}

      {error && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-1 text-sm text-red-600"
        >
          {error}
        </motion.p>
      )}
    </motion.div>
  );
};

export default InputField;
