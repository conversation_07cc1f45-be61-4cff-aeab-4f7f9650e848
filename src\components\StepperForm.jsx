import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FiUser, FiUserCheck, FiSettings, FiCheck } from "react-icons/fi";
import { FaUserGraduate, FaUserMd, FaUserTie } from "react-icons/fa";
import Swal from "sweetalert2";
import InputField from "./InputField";
import TabsSelector from "./TabsSelector";
import {
  validatePassword,
  validateCollegeId,
  validateName,
  validateFile,
  validateDepartment,
  validateSmartZoneEmail,
  getPasswordStrength,
} from "../utils/validation";
import { useAuth } from "../contexts/AuthContext";

const StepperForm = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedRole, setSelectedRole] = useState("");
  const [selectedOperation, setSelectedOperation] = useState("");
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    department: "",
    collegeId: "",
    password: "",
    confirmPassword: "",
    idCardPhoto: null,
    profilePhoto: null,
    email: "",
    adminPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();

  const steps = [
    { id: 1, title: "Select Role", icon: FiUser },
    { id: 2, title: "Choose Operation", icon: FiUserCheck },
    { id: 3, title: "Complete Form", icon: FiSettings },
  ];

  const roleOptions = [
    {
      value: "student",
      label: "Student",
      icon: <FaUserGraduate className="text-xl" />,
    },
    {
      value: "doctor",
      label: "Doctor",
      icon: <FaUserMd className="text-xl" />,
    },
    {
      value: "affairs",
      label: "Affairs",
      icon: <FaUserTie className="text-xl" />,
    },
    {
      value: "admin",
      label: "Admin",
      icon: <FiSettings className="text-xl" />,
    },
  ];

  const operationOptions = [
    { value: "login", label: "Login", icon: <FiUser className="text-lg" /> },
    {
      value: "register",
      label: "Register",
      icon: <FiUserCheck className="text-lg" />,
    },
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateStep = () => {
    const newErrors = {};

    if (currentStep === 1 && !selectedRole) {
      newErrors.role = "Please select a role";
    }

    if (currentStep === 2 && !selectedOperation) {
      newErrors.operation = "Please select an operation";
    }

    if (currentStep === 3) {
      if (selectedOperation === "login") {
        if (selectedRole === "student") {
          // Student login with email
          const emailValidation = validateSmartZoneEmail(formData.email);
          if (!emailValidation.isValid) {
            newErrors.email = emailValidation.error;
          }
        } else {
          // Doctor/Affairs/Admin login with College ID
          const collegeIdValidation = validateCollegeId(formData.collegeId);
          if (!collegeIdValidation.isValid) {
            newErrors.collegeId = collegeIdValidation.error;
          }
        }

        const passwordValidation = validatePassword(formData.password);
        if (!passwordValidation.isValid) {
          newErrors.password = passwordValidation.errors.join(", ");
        }
      } else if (selectedOperation === "register") {
        // Registration validation
        const firstNameValidation = validateName(formData.firstName);
        if (!firstNameValidation.isValid)
          newErrors.firstName = firstNameValidation.error;

        const lastNameValidation = validateName(formData.lastName);
        if (!lastNameValidation.isValid)
          newErrors.lastName = lastNameValidation.error;

        const departmentValidation = validateDepartment(formData.department);
        if (!departmentValidation.isValid)
          newErrors.department = departmentValidation.error;

        if (selectedRole === "admin") {
          // Admin uses email instead of College ID
          const emailValidation = validateSmartZoneEmail(formData.email);
          if (!emailValidation.isValid) {
            newErrors.email = emailValidation.error;
          }
        } else {
          // Doctor/Affairs use College ID
          const collegeIdValidation = validateCollegeId(formData.collegeId);
          if (!collegeIdValidation.isValid)
            newErrors.collegeId = collegeIdValidation.error;
        }

        const passwordValidation = validatePassword(formData.password);
        if (!passwordValidation.isValid) {
          newErrors.password = passwordValidation.errors.join(", ");
        }

        if (formData.password !== formData.confirmPassword) {
          newErrors.confirmPassword = "Passwords do not match";
        }

        const idCardValidation = validateFile(formData.idCardPhoto, "image");
        if (!idCardValidation.isValid)
          newErrors.idCardPhoto = idCardValidation.error;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      if (
        currentStep === 2 &&
        selectedRole === "student" &&
        selectedOperation === "register"
      ) {
        Swal.fire({
          icon: "info",
          title: "Registration Not Available",
          text: "Students can only login. Registration must be done through the mobile app.",
          confirmButtonColor: "#3B82F6",
        });
        return;
      }
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep()) return;

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      if (selectedOperation === "login") {
        // Regular login - check demo accounts
        const demoAccounts = {
          student: { email: "<EMAIL>", password: "Student123!" },
          doctor: { collegeId: "789012", password: "Doctor123!" },
          affairs: { collegeId: "345678", password: "Affairs123!" },
          admin: { collegeId: "111111", password: "Admin123!" },
        };

        const account = demoAccounts[selectedRole];
        let isValidLogin = false;

        if (selectedRole === "student") {
          isValidLogin =
            account &&
            formData.email === account.email &&
            formData.password === account.password;
        } else {
          isValidLogin =
            account &&
            formData.collegeId === account.collegeId &&
            formData.password === account.password;
        }

        if (isValidLogin) {
          login({
            email: selectedRole === "student" ? formData.email : undefined,
            collegeId:
              selectedRole !== "student" ? formData.collegeId : undefined,
            role: selectedRole,
          });
          Swal.fire({
            icon: "success",
            title: "Login Successful!",
            text: `Welcome back! Redirecting to your ${selectedRole} dashboard.`,
            confirmButtonColor: "#10B981",
          });
        } else {
          throw new Error(
            "Invalid credentials. Please check your login information and password."
          );
        }
      } else {
        // Registration - add to pending requests
        const registrationData = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          department: formData.department,
          email: selectedRole === "admin" ? formData.email : undefined,
          collegeId: selectedRole !== "admin" ? formData.collegeId : undefined,
          role: selectedRole,
          idCardPhoto: formData.idCardPhoto,
          profilePhoto: formData.profilePhoto,
        };

        register(registrationData);

        Swal.fire({
          icon: "info",
          title: "Registration Submitted!",
          text: "Your registration request has been sent to the admin for approval. You will be notified once approved.",
          confirmButtonColor: "#3B82F6",
        });

        // Reset form
        setCurrentStep(1);
        setSelectedRole("");
        setSelectedOperation("");
        setFormData({
          firstName: "",
          lastName: "",
          department: "",
          collegeId: "",
          password: "",
          confirmPassword: "",
          idCardPhoto: null,
          profilePhoto: null,
          email: "",
          adminPassword: "",
        });
      }
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Error!",
        text: error.message || "Something went wrong. Please try again.",
        confirmButtonColor: "#EF4444",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const passwordStrength = formData.password
    ? getPasswordStrength(formData.password)
    : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl overflow-hidden "
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 p-6 text-white">
          <h1 className="text-3xl font-bold text-center mb-6">SmartZone</h1>

          {/* Stepper */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <motion.div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    currentStep >= step.id
                      ? "bg-white text-blue-600"
                      : "bg-blue-500 text-white"
                  }`}
                  whileHover={{ scale: 1.05 }}
                >
                  {currentStep > step.id ? (
                    <FiCheck className="text-lg" />
                  ) : (
                    <step.icon className="text-lg" />
                  )}
                </motion.div>
                {index < steps.length - 1 && (
                  <div
                    className={`w-16 h-1 mx-2 ${
                      currentStep > step.id ? "bg-white" : "bg-blue-500"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-4">
            <h2 className="text-xl font-semibold">
              {steps.find((step) => step.id === currentStep)?.title}
            </h2>
          </div>
        </div>

        {/* Form Content */}
        <div className="p-8">
          <AnimatePresence mode="wait">
            {currentStep === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <h3 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
                  Select Your Role
                </h3>
                <TabsSelector
                  options={roleOptions}
                  selectedValue={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  name="role"
                />
                {errors.role && (
                  <p className="text-red-600 text-sm mt-2">{errors.role}</p>
                )}
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <h3 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
                  Choose Operation
                </h3>
                {selectedRole === "student" ? (
                  <div className="text-center">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                      <FaUserGraduate className="text-4xl text-blue-600 mx-auto mb-4" />
                      <h4 className="text-lg font-semibold text-blue-800 mb-2">
                        Student Login Only
                      </h4>
                      <p className="text-blue-600">
                        Students can only login to the system. Registration must
                        be completed through the mobile application.
                      </p>
                    </div>
                    <button
                      onClick={() => {
                        setSelectedOperation("login");
                        handleNext();
                      }}
                      className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                    >
                      Proceed to Login
                    </button>
                  </div>
                ) : (
                  <>
                    <TabsSelector
                      options={operationOptions}
                      selectedValue={selectedOperation}
                      onChange={(e) => setSelectedOperation(e.target.value)}
                      name="operation"
                    />
                    {errors.operation && (
                      <p className="text-red-600 text-sm mt-2">
                        {errors.operation}
                      </p>
                    )}
                  </>
                )}
              </motion.div>
            )}

            {currentStep === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
                className="max-h-96 overflow-y-auto p-4"
              >
                <h3 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
                  {selectedOperation === "login"
                    ? "Login to Your Account"
                    : "Create Your Account"}
                </h3>

                {selectedOperation === "login" ? (
                  <>
                    <InputField
                      type={selectedRole === "student" ? "email" : "text"}
                      label={
                        selectedRole === "student" ? "Email" : "College ID"
                      }
                      name={selectedRole === "student" ? "email" : "collegeId"}
                      value={
                        selectedRole === "student"
                          ? formData.email
                          : formData.collegeId
                      }
                      onChange={handleInputChange}
                      placeholder={
                        selectedRole === "student"
                          ? "Enter your email"
                          : "Enter your college ID"
                      }
                      error={
                        selectedRole === "student"
                          ? errors.email
                          : errors.collegeId
                      }
                      required
                    />
                    <InputField
                      type="password"
                      label="Password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Enter your password"
                      error={errors.password}
                      required
                    />

                    {/* Demo Accounts Info */}
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <h4 className="text-sm font-semibold text-blue-800 mb-2">
                        Demo Accounts:
                      </h4>
                      <div className="text-xs text-blue-700 space-y-1">
                        {selectedRole === "student" && (
                          <p>
                            <strong>Student:</strong> Email:
                            <EMAIL>, Password: Student123!
                          </p>
                        )}
                        {selectedRole === "doctor" && (
                          <p>
                            <strong>Doctor:</strong> ID: 789012, Password:
                            Doctor123!
                          </p>
                        )}
                        {selectedRole === "affairs" && (
                          <p>
                            <strong>Affairs:</strong> ID: 345678, Password:
                            Affairs123!
                          </p>
                        )}
                        {selectedRole === "admin" && (
                          <p>
                            <strong>Admin:</strong> ID: 111111, Password:
                            Admin123!
                          </p>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
                      <InputField
                        type="text"
                        label="First Name"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        placeholder="Enter first name"
                        error={errors.firstName}
                        required
                      />
                      <InputField
                        type="text"
                        label="Last Name"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        placeholder="Enter last name"
                        error={errors.lastName}
                        required
                      />
                    </div>
                    <InputField
                      type="text"
                      label="Department"
                      name="department"
                      value={formData.department}
                      onChange={handleInputChange}
                      placeholder="Enter your department"
                      error={errors.department}
                      required
                    />
                    <InputField
                      type={selectedRole === "admin" ? "email" : "text"}
                      label={selectedRole === "admin" ? "Email" : "College ID"}
                      name={selectedRole === "admin" ? "email" : "collegeId"}
                      value={
                        selectedRole === "admin"
                          ? formData.email
                          : formData.collegeId
                      }
                      onChange={handleInputChange}
                      placeholder={
                        selectedRole === "admin"
                          ? "Enter your email"
                          : "Enter your college ID"
                      }
                      error={
                        selectedRole === "admin"
                          ? errors.email
                          : errors.collegeId
                      }
                      required
                    />
                    <InputField
                      type="password"
                      label="Password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Create a strong password"
                      error={errors.password}
                      required
                      showPasswordStrength
                      passwordStrength={passwordStrength}
                    />
                    <InputField
                      type="password"
                      label="Confirm Password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      placeholder="Confirm your password"
                      error={errors.confirmPassword}
                      required
                    />
                    <InputField
                      type="file"
                      label="ID Card Photo"
                      name="idCardPhoto"
                      value={formData.idCardPhoto}
                      onChange={handleInputChange}
                      accept="image/*"
                      error={errors.idCardPhoto}
                      required
                    />
                    <InputField
                      type="file"
                      label="Profile Photo (Optional)"
                      name="profilePhoto"
                      value={formData.profilePhoto}
                      onChange={handleInputChange}
                      accept="image/*"
                      error={errors.profilePhoto}
                    />
                  </>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <motion.button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className={`px-6 py-3 rounded-lg font-semibold transition-all ${
                currentStep === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : "bg-gray-600 text-white hover:bg-gray-700"
              }`}
              whileHover={currentStep > 1 ? { scale: 1.05 } : {}}
              whileTap={currentStep > 1 ? { scale: 0.95 } : {}}
            >
              Previous
            </motion.button>

            <motion.button
              onClick={currentStep === 3 ? handleSubmit : handleNext}
              disabled={isLoading}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all disabled:opacity-50"
              whileHover={!isLoading ? { scale: 1.05 } : {}}
              whileTap={!isLoading ? { scale: 0.95 } : {}}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Processing...
                </div>
              ) : currentStep === 3 ? (
                selectedOperation === "login" ? (
                  "Login"
                ) : (
                  "Register"
                )
              ) : (
                "Next"
              )}
            </motion.button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default StepperForm;
