import React, { useState, useEffect } from "react";
import {
  HiChevronUp,
  HiChevronDown,
  HiBookOpen,
  HiAcademicCap,
  HiClock,
  HiDocumentText,
  HiUserGroup,
  HiCheckCircle,
} from "react-icons/hi2";
import CollegeGuideLogo from "../../assets/CollegeGuide.jpg";

// Custom CSS for animations
const customStyles = `
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  .animate-pulse-custom {
    animation: pulse 2s infinite;
  }
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = customStyles;
  document.head.appendChild(styleSheet);
}

const CollegeGuide = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [openAccordions, setOpenAccordions] = useState({});

  // Show/hide scroll to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.pageYOffset > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // Toggle accordion
  const toggleAccordion = (id) => {
    setOpenAccordions((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Guide sections data
  const guideSections = [
    {
      id: 1,
      title: "هوية الوثيقة ومجالها",
      icon: <HiDocumentText className="w-6 h-6" />,
      content: `اللائحة موجّهة لمرحلة البكالوريوس بنظام الساعات المعتمدة بكلية الحاسبات والمعلومات – جامعة الزقازيق، ومؤرخة سنة 2019.

الهدف منها تنظيم الدراسة، الامتحانات، التقييم، وشروط الحصول على الدرجة، مع ملحقات كبيرة لمحتويات المقررات والجداول الدراسية.

تعتبر هذه اللائحة المرجع الأساسي لجميع الطلاب والأساتذة والإدارة في تنظيم العملية التعليمية وضمان جودة التعليم وفق المعايير الأكاديمية المعتمدة.`,
      color: "bg-blue-50 border-blue-200",
    },
    {
      id: 2,
      title: "هيكل اللائحة",
      icon: <HiBookOpen className="w-6 h-6" />,
      content: `تبدأ اللائحة بتمهيد، ثم مواد تنظّم الجوانب التالية:

• الدرجة العلمية والقبول
• نظام الدراسة ولغة التدريس
• الإرشاد الأكاديمي والتسجيل
• الإضافة والحذف والانسحاب
• المواظبة والغياب والانقطاع
• الفصل من الكلية
• نظام الامتحانات والتقويم
• الرسوب والإعادة
• الانتقال بين المستويات
• متطلبات الحصول على الدرجة
• التدريب العملي والميداني
• مشروع التخرج
• الإشراف العلمي على تدريس المقررات
• التعليم الإلكتروني وعن بُعد
• بيان الدرجات ونظام الاستماع
• تطبيق اللائحة وقواعد النظام الكودي للمقررات`,
      color: "bg-green-50 border-green-200",
    },
    {
      id: 3,
      title: "الدرجة العلمية ونطاق البرامج",
      icon: <HiAcademicCap className="w-6 h-6" />,
      content: `تنص اللائحة على منح درجة بكالوريوس الحاسبات والمعلومات وفق نظام الساعات المعتمدة.

البرامج المتاحة:
• علوم الحاسب (Computer Science)
• نظم المعلومات (Information Systems)
• تكنولوجيا المعلومات (Information Technology)
• بحوث العمليات ودعم القرار (Operations Research & Decision Support)

يتضمن كل برنامج:
- مقررات عامة على مستوى الكلية
- مقررات تخصصية لكل قسم
- جداول دراسية محددة
- محتوى علمي مفصل لكل مجموعة مقررات

تهدف البرامج إلى إعداد خريجين مؤهلين للعمل في مجالات التكنولوجيا والمعلومات المختلفة.`,
      color: "bg-purple-50 border-purple-200",
    },
    {
      id: 4,
      title: "نظام الدراسة",
      icon: <HiClock className="w-6 h-6" />,
      content: `نظام الساعات المعتمدة مع فصلين دراسيين أساسيين في العام الواحد.

خصائص النظام:
• مرونة في اختيار المقررات حسب الخطة الدراسية
• إمكانية التسجيل في مقررات إضافية أو حذف مقررات
• نظام انسحاب محدد بمواعيد وقواعد واضحة
• ضوابط صارمة للحضور والغياب
• شروط محددة لدخول الامتحان النهائي

المواعيد المهمة:
- فترة التسجيل والإضافة/الحذف
- مواعيد الانسحاب من المقررات
- فترات الامتحانات النصفية والنهائية
- مواعيد إعلان النتائج

قواعد خاصة:
- التعامل مع حالات الانقطاع
- إجراءات الفصل من الكلية
- نظام الإنذارات الأكاديمية`,
      color: "bg-yellow-50 border-yellow-200",
    },
    {
      id: 5,
      title: "المواظبة والغياب",
      icon: <HiUserGroup className="w-6 h-6" />,
      content: `شروط الحضور الإجبارية:

الحد الأدنى للحضور:
• 75% من إجمالي المحاضرات والتمارين لكل مقرر
• يُحسب الغياب من تاريخ بداية الدراسة الفعلية

عواقب تجاوز نسبة الغياب:
• تجاوز 25% غياب بدون عذر مقبول = حرمان من دخول الامتحان النهائي
• تسجيل "راسب" في المقرر
• ضرورة إعادة المقرر في فصل دراسي لاحق

الأعذار المقبولة:
• الأعذار المرضية المعتمدة من الجهات الطبية
• الأعذار القهرية المعتمدة من مجلس الكلية
• الأعذار الرسمية (خدمة عسكرية، مهام رسمية)

في حالة العذر المقبول:
- يُسجل "منسحب" بدلاً من "راسب"
- إمكانية احتساب "غير مكتمل" بشروط معينة
- ضرورة تقديم العذر خلال المدة المحددة`,
      color: "bg-red-50 border-red-200",
    },
    {
      id: 6,
      title: "التسجيل والإضافة/الحذف والانسحاب",
      icon: <HiClock className="w-6 h-6" />,
      content: `نظام التسجيل المرن:

فترة التسجيل:
• نافذة زمنية محددة في بداية كل فصل دراسي
• إمكانية التسجيل في المقررات حسب المستوى الأكاديمي
• ضرورة استيفاء المتطلبات السابقة للمقررات

الإضافة والحذف:
• فترة محددة للإضافة والحذف (عادة أسبوعين من بداية الفصل)
• إمكانية تعديل الجدول الدراسي خلال هذه الفترة
• ضرورة موافقة المرشد الأكاديمي

الانسحاب من المقررات:
• إمكانية الانسحاب حتى منتصف الفصل الدراسي
• الانسحاب بعد الفترة المحددة = تسجيل "راسب"
• ضرورة وجود عذر مقبول من مجلس الكلية للانسحاب المتأخر

حالات خاصة:
- الانقطاع عن الدراسة وشروط العودة
- إجراءات الفصل من الكلية
- نظام الإنذارات والمتابعة الأكاديمية`,
      color: "bg-orange-50 border-orange-200",
    },
    {
      id: 7,
      title: "نظام الامتحانات والتقويم",
      icon: <HiCheckCircle className="w-6 h-6" />,
      content: `نظام تقويم شامل ومتوازن:

مكونات التقييم:
• أعمال السنة (40-50% من الدرجة الكلية)
  - امتحانات نصفية
  - تكليفات ومشاريع
  - مشاركة وحضور
• الامتحان النهائي (50-60% من الدرجة الكلية)

شروط دخول الامتحان النهائي:
• حضور لا يقل عن 75% من المحاضرات
• أداء الأعمال المطلوبة خلال الفصل
• عدم وجود مخالفات أكاديمية

نظام الدرجات:
• A: ممتاز (90-100) = 4.0 نقاط
• B: جيد جداً (80-89) = 3.0 نقاط
• C: جيد (70-79) = 2.0 نقاط
• D: مقبول (60-69) = 1.0 نقطة
• F: راسب (أقل من 60) = 0.0 نقطة

قواعد الرسوب والإعادة:
- الطالب الذي يرسب في مقرر أكثر من مرة يُحتسب الرسوب مرة واحدة فقط في المعدل
- تبقى جميع محاولات الرسوب مثبتة في السجل الأكاديمي
- إمكانية تحسين الدرجات للمقررات المجتازة`,
      color: "bg-indigo-50 border-indigo-200",
    },
    {
      id: 8,
      title: "نظام الاستماع والتعليم الإلكتروني",
      icon: <HiBookOpen className="w-6 h-6" />,
      content: `أنظمة تعليمية متطورة ومرنة:

نظام الاستماع:
• قبول طلاب من داخل وخارج مصر كمستمعين
• موافقة مجلس الكلية ومجالس الأقسام المختصة
• تسديد الرسوم المقررة للمقررات المستمعة
• منح إفادة بالمقررات المستمعة (بدون درجة جامعية)
• شروط وقواعد محددة يضعها مجلس الكلية

التعليم الإلكتروني وعن بُعد:
• استخدام منصات التعلم الإلكتروني الحديثة
• محاضرات مسجلة ومباشرة عبر الإنترنت
• تفاعل إلكتروني بين الطلاب والأساتذة
• امتحانات إلكترونية آمنة
• مكتبة رقمية شاملة

الإشراف العلمي:
• تنظيم الإشراف على تدريس المقررات
• ضمان جودة المحتوى العلمي
• متابعة أداء أعضاء هيئة التدريس
• تطوير المناهج والمقررات باستمرار
• تقييم فعالية طرق التدريس المختلفة`,
      color: "bg-teal-50 border-teal-200",
    },
  ];

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100"
      dir="rtl"
    >
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 shadow-2xl">
        <div className="container mx-auto px-6 py-12">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-reverse space-x-6 mb-6 md:mb-0">
              <img
                src={CollegeGuideLogo}
                alt="College Guide Logo"
                className="w-20 h-20 rounded-full shadow-2xl border-4 border-white/30 backdrop-blur-sm"
              />
              <div>
                <h1 className="text-5xl font-bold text-white mb-3 drop-shadow-lg">
                  دليل الكلية
                </h1>
                <p className="text-xl text-blue-100 font-medium">
                  كلية الحاسبات والمعلومات - جامعة الزقازيق
                </p>
              </div>
            </div>
            <div className="bg-white/20 backdrop-blur-md px-8 py-4 rounded-xl border border-white/30">
              <p className="text-white font-bold text-lg">لائحة 2019</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-12">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <div className="group bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-xl p-8 border border-blue-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold text-blue-800 mb-3">
                  إجمالي الساعات
                </h3>
                <p className="text-4xl font-black text-blue-600 mb-2">138</p>
                <p className="text-sm font-medium text-blue-700">ساعة معتمدة</p>
              </div>
              <div className="bg-blue-500 p-4 rounded-full group-hover:bg-blue-600 transition-colors">
                <HiAcademicCap className="w-8 h-8 text-white" />
              </div>
            </div>
          </div>

          <div className="group bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl shadow-xl p-8 border border-emerald-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold text-emerald-800 mb-3">
                  المعدل المطلوب
                </h3>
                <p className="text-4xl font-black text-emerald-600 mb-2">
                  2.00
                </p>
                <p className="text-sm font-medium text-emerald-700">
                  حد أدنى للتخرج
                </p>
              </div>
              <div className="bg-emerald-500 p-4 rounded-full group-hover:bg-emerald-600 transition-colors">
                <HiCheckCircle className="w-8 h-8 text-white" />
              </div>
            </div>
          </div>

          <div className="group bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl shadow-xl p-8 border border-purple-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold text-purple-800 mb-3">
                  نسبة الحضور
                </h3>
                <p className="text-4xl font-black text-purple-600 mb-2">75%</p>
                <p className="text-sm font-medium text-purple-700">
                  حد أدنى مطلوب
                </p>
              </div>
              <div className="bg-purple-500 p-4 rounded-full group-hover:bg-purple-600 transition-colors">
                <HiUserGroup className="w-8 h-8 text-white" />
              </div>
            </div>
          </div>

          <div className="group bg-gradient-to-br from-amber-50 to-amber-100 rounded-2xl shadow-xl p-8 border border-amber-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold text-amber-800 mb-3">
                  مشروع التخرج
                </h3>
                <p className="text-4xl font-black text-amber-600 mb-2">6</p>
                <p className="text-sm font-medium text-amber-700">
                  ساعات معتمدة
                </p>
              </div>
              <div className="bg-amber-500 p-4 rounded-full group-hover:bg-amber-600 transition-colors">
                <HiClock className="w-8 h-8 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Accordion Sections */}
        <div className="space-y-8 mb-16">
          {guideSections.map((section) => (
            <div
              key={section.id}
              className={`bg-white rounded-2xl shadow-xl border-2 ${section.color} overflow-hidden hover:shadow-2xl transition-all duration-300`}
            >
              <button
                onClick={() => toggleAccordion(section.id)}
                className="w-full px-8 py-6 text-right flex items-center justify-between hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 transition-all duration-300 group"
              >
                <div className="flex items-center space-x-reverse space-x-4">
                  <div className="p-3 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white group-hover:scale-110 transition-transform">
                    {section.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 group-hover:text-blue-700 transition-colors">
                    {section.title}
                  </h3>
                </div>
                <div className="p-2 rounded-full bg-gray-100 group-hover:bg-blue-100 transition-colors">
                  {openAccordions[section.id] ? (
                    <HiChevronUp className="w-6 h-6 text-gray-600 group-hover:text-blue-600" />
                  ) : (
                    <HiChevronDown className="w-6 h-6 text-gray-600 group-hover:text-blue-600" />
                  )}
                </div>
              </button>
              {openAccordions[section.id] && (
                <div className="px-8 pb-8 animate-fadeIn">
                  <div className="border-t-2 border-gray-100 pt-6">
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6">
                      <p className="text-gray-800 leading-relaxed text-lg whitespace-pre-line font-medium">
                        {section.content}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Academic Levels Table */}
        <div className="bg-gradient-to-br from-white to-blue-50 rounded-2xl shadow-2xl p-8 mb-16 border border-blue-100">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 flex items-center">
            <div className="bg-gradient-to-br from-blue-500 to-indigo-600 p-3 rounded-full text-white ml-4">
              <HiAcademicCap className="w-8 h-8" />
            </div>
            الانتقال بين المستويات الأكاديمية
          </h2>
          <div className="overflow-x-auto rounded-xl shadow-lg">
            <table className="w-full border-collapse bg-white rounded-xl overflow-hidden">
              <thead>
                <tr className="bg-gradient-to-r from-blue-600 to-indigo-700">
                  <th className="px-6 py-4 text-right font-bold text-white text-lg">
                    المستوى
                  </th>
                  <th className="px-6 py-4 text-right font-bold text-white text-lg">
                    الساعات المطلوبة
                  </th>
                  <th className="px-6 py-4 text-right font-bold text-white text-lg">
                    الوصف
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 border-b border-gray-100">
                  <td className="px-6 py-4 font-bold text-blue-800 text-lg">
                    المستوى الأول
                  </td>
                  <td className="px-6 py-4 font-semibold text-blue-600">
                    0 - 28 ساعة
                  </td>
                  <td className="px-6 py-4 text-gray-700">
                    يبقى في المستوى الأول حتى يجتاز 28 ساعة معتمدة
                  </td>
                </tr>
                <tr className="hover:bg-gradient-to-r hover:from-emerald-50 hover:to-green-50 transition-all duration-300 border-b border-gray-100">
                  <td className="px-6 py-4 font-bold text-emerald-800 text-lg">
                    المستوى الثاني
                  </td>
                  <td className="px-6 py-4 font-semibold text-emerald-600">
                    28 - 60 ساعة
                  </td>
                  <td className="px-6 py-4 text-gray-700">
                    ينتقل للثاني بعد اجتياز 28 ساعة معتمدة
                  </td>
                </tr>
                <tr className="hover:bg-gradient-to-r hover:from-purple-50 hover:to-violet-50 transition-all duration-300 border-b border-gray-100">
                  <td className="px-6 py-4 font-bold text-purple-800 text-lg">
                    المستوى الثالث
                  </td>
                  <td className="px-6 py-4 font-semibold text-purple-600">
                    60 - 100 ساعة
                  </td>
                  <td className="px-6 py-4 text-gray-700">
                    ينتقل للثالث بعد اجتياز 60 ساعة معتمدة
                  </td>
                </tr>
                <tr className="hover:bg-gradient-to-r hover:from-amber-50 hover:to-orange-50 transition-all duration-300">
                  <td className="px-6 py-4 font-bold text-amber-800 text-lg">
                    المستوى الرابع
                  </td>
                  <td className="px-6 py-4 font-semibold text-amber-600">
                    100+ ساعة
                  </td>
                  <td className="px-6 py-4 text-gray-700">
                    ينتقل للرابع بعد اجتياز 100 ساعة معتمدة
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Requirements Breakdown */}
        <div className="bg-gradient-to-br from-white to-emerald-50 rounded-2xl shadow-2xl p-8 mb-16 border border-emerald-100">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 flex items-center">
            <div className="bg-gradient-to-br from-emerald-500 to-green-600 p-3 rounded-full text-white ml-4">
              <HiCheckCircle className="w-8 h-8" />
            </div>
            متطلبات الحصول على درجة البكالوريوس
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="group bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border-2 border-blue-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-6">
                <div className="bg-blue-500 p-3 rounded-full text-white group-hover:bg-blue-600 transition-colors">
                  <HiDocumentText className="w-6 h-6" />
                </div>
                <h3 className="text-2xl font-bold text-blue-800 mr-4">
                  متطلبات عامة
                </h3>
              </div>
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 shadow-md">
                  <p className="text-blue-700 text-lg font-bold">
                    <span className="text-2xl font-black">12 ساعة</span> إجمالي
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-blue-600 font-semibold">
                    • 6 ساعات إجبارية
                  </p>
                  <p className="text-blue-600 font-semibold">
                    • 6 ساعات اختيارية
                  </p>
                </div>
              </div>
            </div>

            <div className="group bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-8 border-2 border-emerald-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-6">
                <div className="bg-emerald-500 p-3 rounded-full text-white group-hover:bg-emerald-600 transition-colors">
                  <HiAcademicCap className="w-6 h-6" />
                </div>
                <h3 className="text-2xl font-bold text-emerald-800 mr-4">
                  متطلبات الكلية
                </h3>
              </div>
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 shadow-md">
                  <p className="text-emerald-700 text-lg font-bold">
                    <span className="text-2xl font-black">66 ساعة</span> إجمالي
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-emerald-600 font-semibold">
                    • 24 ساعة علوم أساسية
                  </p>
                  <p className="text-emerald-600 font-semibold">
                    • 42 ساعة علوم حاسب
                  </p>
                </div>
              </div>
            </div>

            <div className="group bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 border-2 border-purple-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-6">
                <div className="bg-purple-500 p-3 rounded-full text-white group-hover:bg-purple-600 transition-colors">
                  <HiBookOpen className="w-6 h-6" />
                </div>
                <h3 className="text-2xl font-bold text-purple-800 mr-4">
                  متطلبات التخصص
                </h3>
              </div>
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 shadow-md">
                  <p className="text-purple-700 text-lg font-bold">
                    <span className="text-2xl font-black">60 ساعة</span> إجمالي
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-purple-600 font-semibold">
                    • 42 ساعة إجبارية
                  </p>
                  <p className="text-purple-600 font-semibold">
                    • 12 ساعة اختيارية
                  </p>
                  <p className="text-purple-600 font-semibold">
                    • 6 ساعات مشروع التخرج
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Academic Departments */}
        <div className="bg-gradient-to-br from-white to-indigo-50 rounded-2xl shadow-2xl p-8 mb-16 border border-indigo-100">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 flex items-center">
            <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-3 rounded-full text-white ml-4">
              <HiBookOpen className="w-8 h-8" />
            </div>
            الأقسام الأكاديمية المتاحة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="group bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl p-6 border-2 border-blue-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="bg-blue-500 p-3 rounded-full text-white group-hover:bg-blue-600 transition-colors">
                  <HiAcademicCap className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-bold text-blue-800 mr-4">
                  علوم الحاسب
                </h3>
              </div>
              <p className="text-gray-700 mb-4">
                Computer Science - يركز على الأسس النظرية والعملية لعلوم الحاسوب
              </p>
              <ul className="space-y-2 text-sm text-blue-700">
                <li>• البرمجة والخوارزميات</li>
                <li>• هندسة البرمجيات</li>
                <li>• الذكاء الاصطناعي</li>
                <li>• أمن المعلومات</li>
              </ul>
            </div>

            <div className="group bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl p-6 border-2 border-emerald-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="bg-emerald-500 p-3 rounded-full text-white group-hover:bg-emerald-600 transition-colors">
                  <HiDocumentText className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-bold text-emerald-800 mr-4">
                  نظم المعلومات
                </h3>
              </div>
              <p className="text-gray-700 mb-4">
                Information Systems - يجمع بين التكنولوجيا وإدارة الأعمال
              </p>
              <ul className="space-y-2 text-sm text-emerald-700">
                <li>• تحليل وتصميم النظم</li>
                <li>• إدارة قواعد البيانات</li>
                <li>• نظم المعلومات الإدارية</li>
                <li>• التجارة الإلكترونية</li>
              </ul>
            </div>

            <div className="group bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl p-6 border-2 border-purple-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="bg-purple-500 p-3 rounded-full text-white group-hover:bg-purple-600 transition-colors">
                  <HiClock className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-bold text-purple-800 mr-4">
                  تكنولوجيا المعلومات
                </h3>
              </div>
              <p className="text-gray-700 mb-4">
                Information Technology - يركز على التطبيق العملي للتكنولوجيا
              </p>
              <ul className="space-y-2 text-sm text-purple-700">
                <li>• إدارة الشبكات</li>
                <li>• تطوير التطبيقات</li>
                <li>• الحوسبة السحابية</li>
                <li>• دعم النظم التقنية</li>
              </ul>
            </div>

            <div className="group bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl p-6 border-2 border-amber-200 hover:shadow-2xl hover:scale-105 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="bg-amber-500 p-3 rounded-full text-white group-hover:bg-amber-600 transition-colors">
                  <HiCheckCircle className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-bold text-amber-800 mr-4">
                  بحوث العمليات ودعم القرار
                </h3>
              </div>
              <p className="text-gray-700 mb-4">
                Operations Research & Decision Support - يركز على التحليل الكمي
                واتخاذ القرارات
              </p>
              <ul className="space-y-2 text-sm text-amber-700">
                <li>• النمذجة الرياضية</li>
                <li>• تحليل البيانات</li>
                <li>• نظم دعم القرار</li>
                <li>• الإحصاء التطبيقي</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-xl shadow-lg p-6 mb-12 border-2 border-red-200">
          <h2 className="text-2xl font-bold text-red-800 mb-6 flex items-center">
            <HiDocumentText className="w-8 h-8 text-red-600 ml-3" />
            ملحوظات مهمة للطالب
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="bg-white rounded-lg p-4 border-r-4 border-red-500">
                <h4 className="font-bold text-red-800 mb-2">الحضور والغياب</h4>
                <p className="text-gray-700">
                  الالتزام بالحضور أساسي للسماح بدخول الامتحان النهائي (حد أدنى
                  75%). تجاوز 25% غياب بلا عذر يؤدي للحرمان و"راسب".
                </p>
              </div>
              <div className="bg-white rounded-lg p-4 border-r-4 border-blue-500">
                <h4 className="font-bold text-blue-800 mb-2">
                  التقدم بين المستويات
                </h4>
                <p className="text-gray-700">
                  مرتبط بعدد الساعات المجتازة (28/60/100)، فخطّط لتوزيع مقرراتك
                  بما يضمن عدم تعطّل انتقالك.
                </p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="bg-white rounded-lg p-4 border-r-4 border-green-500">
                <h4 className="font-bold text-green-800 mb-2">
                  متطلبات التخرج
                </h4>
                <p className="text-gray-700">
                  138 ساعة ومعدل تراكمي ≥ 2.00، مع إتمام التدريب والمشروع. لا
                  يُحتسب مقرر "حقوق الإنسان ومكافحة الفساد" في المعدل التراكمي.
                </p>
              </div>
              <div className="bg-white rounded-lg p-4 border-r-4 border-purple-500">
                <h4 className="font-bold text-purple-800 mb-2">
                  التدريب والمشروع
                </h4>
                <p className="text-gray-700">
                  لهما وزن وتأثير فعلي في تأهيلك المهني والعلمي. التزم بالتقرير،
                  وتفاعل مع المشرفين.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Training and Project Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-blue-500">
            <h3 className="text-xl font-bold text-blue-800 mb-4 flex items-center">
              <HiUserGroup className="w-6 h-6 ml-2" />
              التدريب العملي والميداني
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">التوقيت:</span> خلال عطلة
                  صيفية بعد اجتياز 60 ساعة معتمدة
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">الإشراف:</span> تحت إشراف
                  أعضاء هيئة التدريس
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">التقرير:</span> إعداد تقرير عن
                  فترة التدريب وتسليمه
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">شرط للتخرج:</span> نجاح الطالب
                  في التدريب شرط للحصول على البكالوريوس
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-green-500">
            <h3 className="text-xl font-bold text-green-800 mb-4 flex items-center">
              <HiClock className="w-6 h-6 ml-2" />
              مشروع التخرج
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">البداية:</span> عادة في
                  المستوى الرابع
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">المدة:</span> فترة مكثفة تصل
                  إلى أربع أسابيع
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">التقييم:</span> تقرير علمي
                  ومناقشة أمام لجنة
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">القيمة:</span> 6 ساعات معتمدة
                  على فصلين دراسيين
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
            <HiBookOpen className="w-8 h-8 text-indigo-600 ml-3" />
            معلومات إضافية مهمة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-bold text-indigo-800 mb-3">
                نظام الامتحانات والتقويم
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>توجد مواد مستقلة لنظام الامتحانات والتقويم</span>
                </li>
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>
                    الطالب الذي يرسب في المقرر أكثر من مرة يُحتسب الرسوب مرة
                    واحدة فقط في المعدل
                  </span>
                </li>
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>تبقى كل مرات الرسوب مُثبتة في السجل الأكاديمي</span>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-bold text-indigo-800 mb-3">
                بيان الدرجات والخدمات
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>يحق للخريج أو المنسحب الحصول على بيان درجات</span>
                </li>
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>
                    يجوز منح الطالب الوافد بيان درجات لضرورات التأشيرة
                  </span>
                </li>
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>لا يُمنح البيان مع وجود رسوم دراسية غير مسدّدة</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Success Tips */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl shadow-2xl p-8 mb-16 border border-emerald-200">
          <h2 className="text-3xl font-bold text-emerald-800 mb-8 flex items-center">
            <div className="bg-gradient-to-br from-emerald-500 to-green-600 p-3 rounded-full text-white ml-4">
              <HiCheckCircle className="w-8 h-8" />
            </div>
            نصائح للنجاح الأكاديمي
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-lg border-r-4 border-blue-500">
              <h4 className="font-bold text-blue-800 mb-3 text-lg">
                📚 التخطيط الأكاديمي
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li>• خطط لمقرراتك مسبقاً</li>
                <li>• استشر المرشد الأكاديمي</li>
                <li>• تابع تقدمك نحو التخرج</li>
              </ul>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-lg border-r-4 border-emerald-500">
              <h4 className="font-bold text-emerald-800 mb-3 text-lg">
                ⏰ إدارة الوقت
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li>• التزم بالحضور (75% حد أدنى)</li>
                <li>• نظم وقتك بين الدراسة والأنشطة</li>
                <li>• ابدأ مشاريعك مبكراً</li>
              </ul>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-lg border-r-4 border-purple-500">
              <h4 className="font-bold text-purple-800 mb-3 text-lg">
                🎯 التفوق الأكاديمي
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li>• حافظ على معدل أعلى من 2.00</li>
                <li>• شارك في الأنشطة العلمية</li>
                <li>• اطلب المساعدة عند الحاجة</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Summary */}
        <div className="bg-gradient-to-r from-blue-600 via-indigo-700 to-purple-800 rounded-2xl shadow-2xl p-10 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <h2 className="text-4xl font-bold mb-6 text-center">
              خلاصة تنفيذية مركّزة
            </h2>
            <div className="max-w-4xl mx-auto">
              <p className="text-xl leading-relaxed text-center mb-8">
                اللائحة تنظّم رحلة الطالب منذ القبول وحتى التخرج عبر إطار ساعات
                معتمدة واضح، وضوابط حضور وتقييم دقيقة، وتدرج مرحلي بين المستويات
                مرتبط بالساعات المجتازة، مع متطلبات تخرج محددة (138 ساعة، GPA ≥
                2.00)، وركيزتين تطبيقيتين أساسيتين: التدريب العملي/الميداني
                ومشروع التخرج.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                  <h3 className="font-bold text-lg mb-2">🎓 التخرج</h3>
                  <p className="text-blue-100">138 ساعة معتمدة</p>
                </div>
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                  <h3 className="font-bold text-lg mb-2">📊 المعدل</h3>
                  <p className="text-blue-100">2.00 حد أدنى</p>
                </div>
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                  <h3 className="font-bold text-lg mb-2">📅 الحضور</h3>
                  <p className="text-blue-100">75% إجباري</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 left-8 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white p-4 rounded-full shadow-2xl transition-all duration-300 hover:scale-110 hover:shadow-3xl z-50 border-2 border-white/20 backdrop-blur-sm"
          aria-label="العودة إلى الأعلى"
        >
          <HiChevronUp className="w-7 h-7" />
        </button>
      )}
    </div>
  );
};

export default CollegeGuide;
