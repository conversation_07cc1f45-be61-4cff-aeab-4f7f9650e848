import React from 'react';
import { motion } from 'framer-motion';

const TabsSelector = ({
  options,
  selectedValue,
  onChange,
  name,
  className = '',
  disabled = false
}) => {
  return (
    <div className={`mb-6 ${className}`}>
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {options.map((option) => (
          <motion.button
            key={option.value}
            type="button"
            onClick={() => !disabled && onChange({ target: { name, value: option.value } })}
            className={`flex-1 py-3 px-4 text-sm font-medium rounded-md transition-all duration-200 relative ${
              selectedValue === option.value
                ? 'text-white shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            whileHover={!disabled ? { scale: 1.02 } : {}}
            whileTap={!disabled ? { scale: 0.98 } : {}}
          >
            {selectedValue === option.value && (
              <motion.div
                layoutId="activeTab"
                className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-md"
                initial={false}
                transition={{
                  type: "spring",
                  stiffness: 500,
                  damping: 30
                }}
              />
            )}
            <span className="relative z-10 flex items-center justify-center">
              {option.icon && <span className="mr-2">{option.icon}</span>}
              {option.label}
            </span>
          </motion.button>
        ))}
      </div>
    </div>
  );
};

export default TabsSelector;
